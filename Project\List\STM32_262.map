Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text) refers to peripheralinit.o(.text) for PeripheralInit
    main.o(.text) refers to max262.o(.text) for Filter1
    main.o(.text) refers to key.o(.text) for Key_Scan
    main.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    main.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    main.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    main.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    main.o(.text) refers to delay.o(.text) for Delay_1ms
    main.o(.text) refers to printf6.o(i.__0sprintf$6) for __2sprintf
    main.o(.text) refers to charlcd.o(.text) for WriteString
    main.o(.text) refers to main.o(.data) for Fn
    main.o(.text) refers to pwmoutput.o(.data) for divf
    peripheralinit.o(.text) refers to charlcd.o(.text) for LCM_Init
    peripheralinit.o(.text) refers to key.o(.text) for Key_Init
    peripheralinit.o(.text) refers to pwmoutput.o(.text) for TIM2_PWMOutput_Init
    peripheralinit.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    peripheralinit.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    max262.o(.text) refers to fdiv.o(.text) for __aeabi_fdiv
    max262.o(.text) refers to fadd.o(.text) for __aeabi_frsub
    max262.o(.text) refers to ffixui.o(.text) for __aeabi_f2uiz
    max262.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    max262.o(.text) refers to delay.o(.text) for Delay_ns
    max262.o(.text) refers to main.o(.data) for Fn
    pwmoutput.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwmoutput.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    pwmoutput.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    pwmoutput.o(.text) refers to pwmoutput.o(.data) for radio_num
    key.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    key.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for Delay_1ms
    charlcd.o(.text) refers to delay.o(.text) for Delay_ns
    charlcd.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_SetBits
    charlcd.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    charlcd.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    charlcd.o(.text) refers to charlcd.o(.data) for lcdbuff
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing misc.o(.text), (220 bytes).
    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

6 unused section(s) (total 2408 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\CoreSupport\core_cm3.c          0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\User\CharLCD.c                        0x00000000   Number         0  charlcd.o ABSOLUTE
    ..\User\Delay.c                          0x00000000   Number         0  delay.o ABSOLUTE
    ..\User\MAX262.c                         0x00000000   Number         0  max262.o ABSOLUTE
    ..\User\PWMOutput.c                      0x00000000   Number         0  pwmoutput.o ABSOLUTE
    ..\User\PeripheralInit.c                 0x00000000   Number         0  peripheralinit.o ABSOLUTE
    ..\User\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\\CMSIS\\CoreSupport\\core_cm3.c       0x00000000   Number         0  core_cm3.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section        0  main.o(.text)
    .text                                    0x0800059a   Section        0  delay.o(.text)
    .text                                    0x080006c4   Section        0  peripheralinit.o(.text)
    .text                                    0x08000710   Section        0  max262.o(.text)
    .text                                    0x08000a1c   Section        0  pwmoutput.o(.text)
    .text                                    0x08000ac4   Section        0  key.o(.text)
    .text                                    0x08000b94   Section        0  charlcd.o(.text)
    .text                                    0x080011ec   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08001548   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x080018ec   Section        0  stm32f10x_tim.o(.text)
    TI4_Config                               0x08001d93   Thumb Code   130  stm32f10x_tim.o(.text)
    TI3_Config                               0x08001e27   Thumb Code   122  stm32f10x_tim.o(.text)
    TI2_Config                               0x08001ebb   Thumb Code   130  stm32f10x_tim.o(.text)
    TI1_Config                               0x08001f4f   Thumb Code   108  stm32f10x_tim.o(.text)
    .text                                    0x08002708   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08002709   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080027df   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080028e8   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800290c   Section        0  ldiv.o(.text)
    .text                                    0x0800296e   Section        0  memseta.o(.text)
    .text                                    0x08002992   Section        0  fadd.o(.text)
    .text                                    0x08002a42   Section        0  fdiv.o(.text)
    .text                                    0x08002abe   Section        0  dadd.o(.text)
    .text                                    0x08002c0c   Section        0  dmul.o(.text)
    .text                                    0x08002cf0   Section        0  ddiv.o(.text)
    .text                                    0x08002dce   Section        0  dflti.o(.text)
    .text                                    0x08002df0   Section        0  dfltui.o(.text)
    .text                                    0x08002e0a   Section        0  ffixui.o(.text)
    .text                                    0x08002e32   Section        0  dfixui.o(.text)
    .text                                    0x08002e64   Section        0  uidiv.o(.text)
    .text                                    0x08002e90   Section        0  uldiv.o(.text)
    .text                                    0x08002ef2   Section        0  llshl.o(.text)
    .text                                    0x08002f10   Section        0  llushr.o(.text)
    .text                                    0x08002f30   Section        0  llsshr.o(.text)
    .text                                    0x08002f54   Section        0  fepilogue.o(.text)
    .text                                    0x08002f54   Section        0  iusefp.o(.text)
    .text                                    0x08002fc2   Section        0  depilogue.o(.text)
    .text                                    0x0800307c   Section       36  init.o(.text)
    i.__0sprintf$6                           0x080030a0   Section        0  printf6.o(i.__0sprintf$6)
    i.__scatterload_copy                     0x080030c8   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080030d6   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080030d8   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x080030e8   Section        0  printf6.o(i._printf_core)
    _printf_core                             0x080030e9   Thumb Code   748  printf6.o(i._printf_core)
    i._printf_post_padding                   0x080033d8   Section        0  printf6.o(i._printf_post_padding)
    _printf_post_padding                     0x080033d9   Thumb Code    36  printf6.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080033fc   Section        0  printf6.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080033fd   Thumb Code    46  printf6.o(i._printf_pre_padding)
    i._sputc                                 0x0800342a   Section        0  printf6.o(i._sputc)
    _sputc                                   0x0800342b   Thumb Code    10  printf6.o(i._sputc)
    .data                                    0x20000000   Section        1  main.o(.data)
    .data                                    0x20000004   Section        8  pwmoutput.o(.data)
    .data                                    0x2000000c   Section      129  charlcd.o(.data)
    .data                                    0x2000008d   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000008d   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000009d   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x200000a4   Section       20  system_stm32f10x.o(.data)
    STACK                                    0x200000b8   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    main                                     0x08000145   Thumb Code  1110  main.o(.text)
    TimingDelay_Decrement                    0x0800059b   Thumb Code     2  delay.o(.text)
    Delay_ns                                 0x0800059d   Thumb Code    12  delay.o(.text)
    Delay_1us                                0x080005a9   Thumb Code    26  delay.o(.text)
    Delay_2us                                0x080005c3   Thumb Code    26  delay.o(.text)
    Delay_10us                               0x080005dd   Thumb Code    36  delay.o(.text)
    Delay_250us                              0x08000601   Thumb Code    36  delay.o(.text)
    Delay_882us                              0x08000625   Thumb Code    26  delay.o(.text)
    Delay_1ms                                0x0800063f   Thumb Code    36  delay.o(.text)
    Delay_5ms                                0x08000663   Thumb Code    38  delay.o(.text)
    Delay_50ms                               0x08000689   Thumb Code    40  delay.o(.text)
    Delay                                    0x080006b1   Thumb Code    18  delay.o(.text)
    PeripheralInit                           0x080006c5   Thumb Code    16  peripheralinit.o(.text)
    MAX262_GPIO_Init                         0x080006d5   Thumb Code    56  peripheralinit.o(.text)
    Qn                                       0x08000711   Thumb Code    40  max262.o(.text)
    Filter1                                  0x08000739   Thumb Code   354  max262.o(.text)
    Filter2                                  0x0800089b   Thumb Code   378  max262.o(.text)
    TIM2_GPIO_Init                           0x08000a1d   Thumb Code    38  pwmoutput.o(.text)
    TIM2_Mode_Init                           0x08000a43   Thumb Code   110  pwmoutput.o(.text)
    TIM2_PWMOutput_Init                      0x08000ab1   Thumb Code    12  pwmoutput.o(.text)
    Key_Init                                 0x08000ac5   Thumb Code    38  key.o(.text)
    Key_Scan                                 0x08000aeb   Thumb Code   164  key.o(.text)
    Wr_CodeData                              0x08000b95   Thumb Code   166  charlcd.o(.text)
    WrCLcdD                                  0x08000c3b   Thumb Code    38  charlcd.o(.text)
    WrCLcdC                                  0x08000c61   Thumb Code    38  charlcd.o(.text)
    CG_Write                                 0x08000c87   Thumb Code    30  charlcd.o(.text)
    GPIO_LCM_Configuration                   0x08000ca5   Thumb Code    64  charlcd.o(.text)
    LCM_Init                                 0x08000ce5   Thumb Code    60  charlcd.o(.text)
    WriteString                              0x08000d21   Thumb Code    52  charlcd.o(.text)
    WrCLcd_char_num                          0x08000d55   Thumb Code   174  charlcd.o(.text)
    WrCLcd_int_num                           0x08000e03   Thumb Code   226  charlcd.o(.text)
    WrCLcd_long_num                          0x08000ee5   Thumb Code   454  charlcd.o(.text)
    Wr_In1                                   0x080010ab   Thumb Code    36  charlcd.o(.text)
    Wr_In2                                   0x080010cf   Thumb Code    36  charlcd.o(.text)
    CL_Enter                                 0x080010f3   Thumb Code    44  charlcd.o(.text)
    CR_Enter                                 0x0800111f   Thumb Code    42  charlcd.o(.text)
    L_Enter                                  0x08001149   Thumb Code    42  charlcd.o(.text)
    R_Enter                                  0x08001173   Thumb Code    42  charlcd.o(.text)
    CGWrite                                  0x0800119d   Thumb Code    62  charlcd.o(.text)
    GPIO_DeInit                              0x080011ed   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08001299   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x080012ad   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x080013c3   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080013d3   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080013e5   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080013ed   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x080013ff   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08001407   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x0800140b   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x0800140f   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08001419   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800141d   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x0800142f   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08001449   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x0800144f   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080014d9   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x0800151b   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08001549   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08001589   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x080015cf   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08001607   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800163f   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08001653   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08001659   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08001671   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08001677   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08001689   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08001693   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x080016a5   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x080016b7   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x080016cb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x080016e5   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x080016ed   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x080016ff   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08001731   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08001737   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08001743   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800174b   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x0800180b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001825   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800183f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08001859   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001873   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x0800188d   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08001895   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x0800189b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x080018a1   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x080018af   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x080018c3   Thumb Code     6  stm32f10x_rcc.o(.text)
    TIM_DeInit                               0x080018ed   Thumb Code   424  stm32f10x_tim.o(.text)
    TIM_TimeBaseInit                         0x08001a95   Thumb Code   122  stm32f10x_tim.o(.text)
    TIM_OC1Init                              0x08001b0f   Thumb Code   132  stm32f10x_tim.o(.text)
    TIM_OC2Init                              0x08001b93   Thumb Code   154  stm32f10x_tim.o(.text)
    TIM_OC3Init                              0x08001c2d   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_OC4Init                              0x08001cc3   Thumb Code   182  stm32f10x_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001d79   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001e15   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001ea1   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001f3d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ICInit                               0x08001fbb   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_PWMIConfig                           0x08002051   Thumb Code   124  stm32f10x_tim.o(.text)
    TIM_BDTRConfig                           0x080020cd   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080020ed   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OCStructInit                         0x080020ff   Thumb Code    20  stm32f10x_tim.o(.text)
    TIM_ICStructInit                         0x08002113   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_BDTRStructInit                       0x08002125   Thumb Code    40  stm32f10x_tim.o(.text)
    TIM_Cmd                                  0x0800214d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08002165   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_ITConfig                             0x08002183   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GenerateEvent                        0x08002195   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_DMAConfig                            0x08002199   Thumb Code    10  stm32f10x_tim.o(.text)
    TIM_DMACmd                               0x080021a3   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_InternalClockConfig                  0x080021b5   Thumb Code    12  stm32f10x_tim.o(.text)
    TIM_SelectInputTrigger                   0x080021c1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080021d3   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_TIxExternalClockConfig               0x080021eb   Thumb Code    62  stm32f10x_tim.o(.text)
    TIM_ETRConfig                            0x08002229   Thumb Code    28  stm32f10x_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08002245   Thumb Code    54  stm32f10x_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800227b   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_PrescalerConfig                      0x0800229b   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_CounterModeConfig                    0x080022a1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x080022b3   Thumb Code    66  stm32f10x_tim.o(.text)
    TIM_ForcedOC1Config                      0x080022f5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC2Config                      0x08002307   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ForcedOC3Config                      0x08002321   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC4Config                      0x08002333   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ARRPreloadConfig                     0x0800234d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCOM                            0x08002365   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCCDMA                          0x0800237d   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CCPreloadControl                     0x08002395   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080023ad   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080023bf   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080023d9   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080023eb   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC1FastConfig                        0x08002405   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2FastConfig                        0x08002417   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3FastConfig                        0x08002431   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4FastConfig                        0x08002443   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800245d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC2Ref                          0x0800246f   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_ClearOC3Ref                          0x08002487   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC4Ref                          0x08002499   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080024b1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080024c3   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080024d5   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x080024ef   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08002509   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08002523   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800253d   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_CCxCmd                               0x08002557   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_CCxNCmd                              0x08002575   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_SelectOCxM                           0x08002593   Thumb Code    82  stm32f10x_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080025e5   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_UpdateRequestConfig                  0x080025fd   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectHallSensor                     0x08002615   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectOnePulseMode                   0x0800262d   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectOutputTrigger                  0x0800263f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002651   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08002663   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetCounter                           0x08002675   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetAutoreload                        0x08002679   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare1                          0x0800267d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare2                          0x08002681   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare3                          0x08002685   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare4                          0x08002689   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_SetClockDivision                     0x0800268f   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GetCapture1                          0x080026a1   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture2                          0x080026a7   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture3                          0x080026ad   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture4                          0x080026b3   Thumb Code     8  stm32f10x_tim.o(.text)
    TIM_GetCounter                           0x080026bb   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetPrescaler                         0x080026c1   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetFlagStatus                        0x080026c7   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearFlag                            0x080026d9   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetITStatus                          0x080026df   Thumb Code    34  stm32f10x_tim.o(.text)
    TIM_ClearITPendingBit                    0x08002701   Thumb Code     6  stm32f10x_tim.o(.text)
    SystemInit                               0x080027e7   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08002835   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x080028e9   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x080028f1   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x080028f3   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x080028f5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x080028f7   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x080028f9   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x080028fb   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x080028fd   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x080028ff   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x08002901   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08002903   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_ldivmod                          0x0800290d   Thumb Code    98  ldiv.o(.text)
    __aeabi_memset                           0x0800296f   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800296f   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800296f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800297d   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800297d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800297d   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08002981   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x08002993   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08002a37   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08002a3d   Thumb Code     6  fadd.o(.text)
    __aeabi_fdiv                             0x08002a43   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08002abf   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08002c01   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08002c07   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08002c0d   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08002cf1   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08002dcf   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08002df1   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2uiz                            0x08002e0b   Thumb Code    40  ffixui.o(.text)
    __aeabi_d2uiz                            0x08002e33   Thumb Code    50  dfixui.o(.text)
    __aeabi_uidiv                            0x08002e65   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08002e65   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08002e91   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08002ef3   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08002ef3   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08002f11   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08002f11   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08002f31   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08002f31   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08002f55   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08002f55   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08002f67   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08002fc3   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08002fe1   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x0800307d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800307d   Thumb Code     0  init.o(.text)
    __0sprintf$6                             0x080030a1   Thumb Code    34  printf6.o(i.__0sprintf$6)
    __1sprintf$6                             0x080030a1   Thumb Code     0  printf6.o(i.__0sprintf$6)
    __2sprintf                               0x080030a1   Thumb Code     0  printf6.o(i.__0sprintf$6)
    __scatterload_copy                       0x080030c9   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080030d7   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080030d9   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x08003434   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003454   Number         0  anon$$obj.o(Region$$Table)
    Fn                                       0x20000000   Data           1  main.o(.data)
    counterf                                 0x20000004   Data           4  pwmoutput.o(.data)
    divf                                     0x20000008   Data           2  pwmoutput.o(.data)
    radio_num                                0x2000000a   Data           2  pwmoutput.o(.data)
    CGTab                                    0x2000000c   Data          64  charlcd.o(.data)
    tab1                                     0x2000004c   Data          16  charlcd.o(.data)
    tab2                                     0x2000005c   Data          15  charlcd.o(.data)
    tabdy                                    0x2000006b   Data          32  charlcd.o(.data)
    lcdbuff                                  0x2000008b   Data           1  charlcd.o(.data)
    lcdbuff_1                                0x2000008c   Data           1  charlcd.o(.data)
    SystemCoreClock                          0x200000a4   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x200000a8   Data          16  system_stm32f10x.o(.data)
    __initial_sp                             0x200004b8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000350c, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003454, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          252    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          257  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          543    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          546    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          548    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          550    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          551    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          553    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          555    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          544    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000456   Code   RO            1    .text               main.o
    0x0800059a   0x0800059a   0x00000128   Code   RO           73    .text               delay.o
    0x080006c2   0x080006c2   0x00000002   PAD
    0x080006c4   0x080006c4   0x0000004c   Code   RO           88    .text               peripheralinit.o
    0x08000710   0x08000710   0x0000030c   Code   RO          100    .text               max262.o
    0x08000a1c   0x08000a1c   0x000000a8   Code   RO          117    .text               pwmoutput.o
    0x08000ac4   0x08000ac4   0x000000d0   Code   RO          132    .text               key.o
    0x08000b94   0x08000b94   0x00000658   Code   RO          144    .text               charlcd.o
    0x080011ec   0x080011ec   0x0000035c   Code   RO          171    .text               stm32f10x_gpio.o
    0x08001548   0x08001548   0x000003a4   Code   RO          183    .text               stm32f10x_rcc.o
    0x080018ec   0x080018ec   0x00000e1a   Code   RO          197    .text               stm32f10x_tim.o
    0x08002706   0x08002706   0x00000002   PAD
    0x08002708   0x08002708   0x000001e0   Code   RO          232    .text               system_stm32f10x.o
    0x080028e8   0x080028e8   0x00000024   Code   RO          253    .text               startup_stm32f10x_hd.o
    0x0800290c   0x0800290c   0x00000062   Code   RO          260    .text               mc_w.l(ldiv.o)
    0x0800296e   0x0800296e   0x00000024   Code   RO          262    .text               mc_w.l(memseta.o)
    0x08002992   0x08002992   0x000000b0   Code   RO          525    .text               mf_w.l(fadd.o)
    0x08002a42   0x08002a42   0x0000007c   Code   RO          527    .text               mf_w.l(fdiv.o)
    0x08002abe   0x08002abe   0x0000014e   Code   RO          529    .text               mf_w.l(dadd.o)
    0x08002c0c   0x08002c0c   0x000000e4   Code   RO          531    .text               mf_w.l(dmul.o)
    0x08002cf0   0x08002cf0   0x000000de   Code   RO          533    .text               mf_w.l(ddiv.o)
    0x08002dce   0x08002dce   0x00000022   Code   RO          535    .text               mf_w.l(dflti.o)
    0x08002df0   0x08002df0   0x0000001a   Code   RO          537    .text               mf_w.l(dfltui.o)
    0x08002e0a   0x08002e0a   0x00000028   Code   RO          539    .text               mf_w.l(ffixui.o)
    0x08002e32   0x08002e32   0x00000032   Code   RO          541    .text               mf_w.l(dfixui.o)
    0x08002e64   0x08002e64   0x0000002c   Code   RO          560    .text               mc_w.l(uidiv.o)
    0x08002e90   0x08002e90   0x00000062   Code   RO          562    .text               mc_w.l(uldiv.o)
    0x08002ef2   0x08002ef2   0x0000001e   Code   RO          564    .text               mc_w.l(llshl.o)
    0x08002f10   0x08002f10   0x00000020   Code   RO          566    .text               mc_w.l(llushr.o)
    0x08002f30   0x08002f30   0x00000024   Code   RO          568    .text               mc_w.l(llsshr.o)
    0x08002f54   0x08002f54   0x00000000   Code   RO          570    .text               mc_w.l(iusefp.o)
    0x08002f54   0x08002f54   0x0000006e   Code   RO          571    .text               mf_w.l(fepilogue.o)
    0x08002fc2   0x08002fc2   0x000000ba   Code   RO          573    .text               mf_w.l(depilogue.o)
    0x0800307c   0x0800307c   0x00000024   Code   RO          579    .text               mc_w.l(init.o)
    0x080030a0   0x080030a0   0x00000028   Code   RO          421    i.__0sprintf$6      mc_w.l(printf6.o)
    0x080030c8   0x080030c8   0x0000000e   Code   RO          585    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080030d6   0x080030d6   0x00000002   Code   RO          586    i.__scatterload_null  mc_w.l(handlers.o)
    0x080030d8   0x080030d8   0x0000000e   Code   RO          587    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080030e6   0x080030e6   0x00000002   PAD
    0x080030e8   0x080030e8   0x000002f0   Code   RO          426    i._printf_core      mc_w.l(printf6.o)
    0x080033d8   0x080033d8   0x00000024   Code   RO          427    i._printf_post_padding  mc_w.l(printf6.o)
    0x080033fc   0x080033fc   0x0000002e   Code   RO          428    i._printf_pre_padding  mc_w.l(printf6.o)
    0x0800342a   0x0800342a   0x0000000a   Code   RO          430    i._sputc            mc_w.l(printf6.o)
    0x08003434   0x08003434   0x00000020   Data   RO          583    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003454, Size: 0x000004b8, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003454   0x00000001   Data   RW            2    .data               main.o
    0x20000001   0x08003455   0x00000003   PAD
    0x20000004   0x08003458   0x00000008   Data   RW          118    .data               pwmoutput.o
    0x2000000c   0x08003460   0x00000081   Data   RW          145    .data               charlcd.o
    0x2000008d   0x080034e1   0x00000014   Data   RW          184    .data               stm32f10x_rcc.o
    0x200000a1   0x080034f5   0x00000003   PAD
    0x200000a4   0x080034f8   0x00000014   Data   RW          233    .data               system_stm32f10x.o
    0x200000b8        -       0x00000400   Zero   RW          250    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1624         88          0        129          0       5136   charlcd.o
         0          0          0          0          0         32   core_cm3.o
       296          0          0          0          0       2613   delay.o
       208          6          0          0          0        760   key.o
      1110         94          0          1          0     247204   main.o
       780          8          0          0          0       1405   max262.o
        76          4          0          0          0        690   peripheralinit.o
       168          8          0          8          0       1286   pwmoutput.o
        36          8        304          0       1024        880   startup_stm32f10x_hd.o
       860         38          0          0          0       5893   stm32f10x_gpio.o
       932         36          0         20          0       9132   stm32f10x_rcc.o
      3610         88          0          0          0      23004   stm32f10x_tim.o
       480         38          0         20          0       2091   system_stm32f10x.o

    ----------------------------------------------------------------------
     10184        <USER>        <GROUP>        184       1024     300126   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
       884         10          0          0          0        420   printf6.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        40          0          0          0          0         68   ffixui.o

    ----------------------------------------------------------------------
      2876         <USER>          <GROUP>          0          0       2268   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1344         26          0          0          0       1056   mc_w.l
      1530          0          0          0          0       1212   mf_w.l

    ----------------------------------------------------------------------
      2876         <USER>          <GROUP>          0          0       2268   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13060        442        336        184       1024     300518   Grand Totals
     13060        442        336        184       1024     300518   ELF Image Totals
     13060        442        336        184          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13396 (  13.08kB)
    Total RW  Size (RW Data + ZI Data)              1208 (   1.18kB)
    Total ROM Size (Code + RO Data + RW Data)      13580 (  13.26kB)

==============================================================================

