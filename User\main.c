//-----------------------------------------------------------------
// Project: STM32 MAX262 Filter Control System
// Author: Modified for STM32F103C8T6
// Date: 2019-05-20
// Version: V1.1 - Keyboard removed version
// Hardware: STM32F103C8T6 + MAX262 Module + LCD1602
// 
// Pin Connections:
//    MAX262 Module     STM32 Board
// 				WR    <--    PE15
// 				LE    <--    PE14
// 				D0    <--    PB8
// 				D1    <--    PB9
// 				A0    <--    PB10
// 				A1    <--    PB11
// 				A2    <--    PB12
// 				A3    <--    PB13
// 				GND   <-->   GND
// 				CLK   <--    PA0
//
// Description:
// 		PWM provides clock signal for MAX262 filter
//		Fixed frequency operation - no keyboard input required
//		Current version: Keyboard functionality removed, displays fixed frequency
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Header Files
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "delay.h"
#include "PeripheralInit.h"
#include "MAX262.h"
#include "CharLCD.h"
#include "PWMOutput.h"
#include "stdio.h"

extern u32 counterf;             // Counter reload value
extern u16 divf;                 // Prescaler value
extern u16 radio_num;
u8 Fn;

//-----------------------------------------------------------------
// Main Function
//-----------------------------------------------------------------
int main(void)
{
	u8 buf[6];
	s32 fre1,fre0;              // fre0: reference value, fre1: current value
	double fclk;								  // CLK frequency in Hz
	fre0 = 0;
	fre1 = 1000;                        // Cutoff frequency in Hz, default 1000Hz
	PeripheralInit();                     // Peripheral initialization
//	WriteString (1, 3, "MAX262 test!");   
//	WriteString (2, 1, "Freq: ");
//	WriteString (2, 14, " Hz ");
	MAX262_GPIO_Init();                  // IO port initialization
// 	Filter1(0, 3);                       // Filter mode, frequency, Q factor
// 	Filter2(0, 3);                       // Filter mode, frequency, Q factor
// 	Delay_1ms(100);		   
// 	GPIOB->ODR =0;
    Fn = 20;                   // Frequency control factor N = 20, f = 1 kHz
    Filter1(1, 0.707);         // Mode 1 + Q=0.707 = Butterworth lowpass
	// Initialize frequency value
	if(fre1 > 100000)  fre1=100000;     // Max cutoff frequency 100000Hz
	else if(fre1 == 0) fre1=1;          // Min cutoff frequency 1Hz

	// Configure frequency
	if(fre1 <= 28000)                 // Check if frequency < 28k
	{
		fclk = (double)(fre1 * NF1);                    // Calculate CLK frequency
		Fn = 63;
	}
	else
	{
		fclk = (double)(fre1 * NF2);                    // Calculate CLK frequency
		Fn = 0;
	}
	divf = ((72000000/fclk)/65535)+1;    // Calculate prescaler
	counterf = ((720000000/fclk)/divf);  // Counter reload value
	if(counterf%10>=5)                   // Round counter value
	{
		counterf = (counterf/10)+1;
	}
	else
	{
		counterf = counterf/10;
	}
	radio_num = counterf/2;            // Duty cycle ratio
	TIM2_PWMOutput_Init();             // Reinitialize PWM
	fre0 =fre1;
	Filter1(0, 0.842);                     // Configure filter mode and Q factor
	Filter2(0, 0.842);                     // Configure filter mode and Q factor
	Delay_1ms(50);
	GPIOB->ODR =0;

	while(1)
	{
		// Display current frequency value
		sprintf((char*)buf, "%6d", fre1);   // Convert number to string
		WriteString (2, 7, buf);            // Display cutoff frequency on LCD1602

		Delay_1ms(100);                     // Delay 100ms
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
