//-----------------------------------------------------------------
// ��������: 
//		��Keil uVision4����ƽ̨�»���STM32�ĳ���ģ��
// ��    ��: ���ǵ���
// ��ʼ����: 2019-05-20
// �������: 2014-05-24
// �޸�����:
// ��    ��: V1.0
//   - V1.0: ����ģ��
// ���Թ���: ����STM32���İ�塢LZE_ST LINK2��MAX262ģ��
// �� �� ˵ ��:
//    MAX262ģ��     STM32���İ�
// 				WR    <--    PE15
// 				LE    <--    PE14
// 				D0    <--    PB8
// 				D1    <--    PB9
// 				A0    <--    PB10
// 				A1    <--    PB11
// 				A2    <--    PB12
// 				A3    <--    PB13
// 				GND   <-->   GND

// 				CLK�˿�<--   PA0

// ��������˵��:
// 		PWM���ṩʱ���ź�ʱ����Ҫ�ð���������������/��ֹƵ�ʣ�
//		�������õ�Ƶ�ʼ����ʱ��Ƶ�ʣ���������װ��ֵ��Ԥ��Ƶֵ�����PWM����
//		����Ԥ��Ƶֵ����װ��ֵ������������˵ó���ʱ��Ƶ�ʴ���һ������
//		��ǰ�汾ɾ�����������ܣ�ֻ��ʾ�̶�Ƶ��ֵ
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// ͷ�ļ����� 
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "delay.h"
#include "PeripheralInit.h"
#include "MAX262.h"
#include "CharLCD.h"
#include "PWMOutput.h"
#include "stdio.h"

extern u32 counterf;             // ��װ��ֵ
extern u16 divf;                 // Ԥ��Ƶ��
extern u16 radio_num;
u8 Fn;

//-----------------------------------------------------------------
// ������
//-----------------------------------------------------------------
int main(void)
{
	u8 buf[6];
	s32 fre1,fre0;              // fre0Ϊ��׼��ֵ, fre1Ϊ���ĺ��ֵ
	double fclk;								  // CLKʱ��, ��λΪHz
	fre0 = 0;
	fre1 = 1000;                        // ��ֹƵ��, ��λΪHz������Ĭ��ֵΪ1000Hz
	PeripheralInit();                     // �����ʼ��
//	WriteString (1, 3, "MAX262 test!");   
//	WriteString (2, 1, "Freq: ");
//	WriteString (2, 14, " Hz ");
	MAX262_GPIO_Init();                  // IO�ڳ�ʼ������
// 	Filter1(0, 3);                       // (mode)���Ʒ�ʽmode, ����Ƶ��f, Ʒ������q
// 	Filter2(0, 3);                       // (mode)���Ʒ�ʽmode, ����Ƶ��f, Ʒ������q
// 	Delay_1ms(100);		   
// 	GPIOB->ODR =0;
    Fn = 20;                   // Ƶ�ʿ����� N �� 20 �� f? �� 1 kHz
    Filter1(1, 0.707);         // ģʽ1 + Q=0.707 �� ���׵�ͨ
	// ��ʼ��Ƶ��ֵ
	if(fre1 > 100000)  fre1=100000;     // ��ֹƵ�����Ϊ100000Hz
	else if(fre1 == 0) fre1=1;          // ����Ƶ����СΪ1Hz

	// ����Ƶ��
	if(fre1 <= 28000)                 // �ж�Ƶ�ʴ���28k, ������ѡ��40.48, ����Ϊ139.8
	{
		fclk = (double)(fre1 * NF1);                    // ���CLKʱ��, оƬ�ֲ�p11
		Fn = 63;
	}
	else
	{
		fclk = (double)(fre1 * NF2);                    // ���CLKʱ��, оƬ�ֲ�p11
		Fn = 0;
	}
	divf = ((72000000/fclk)/65535)+1;    // ���Ԥ��Ƶ��
	counterf = ((720000000/fclk)/divf);  // ��װ��ֵ
	if(counterf%10>=5)                   // ��װ��ֵ��������
	{
		counterf = (counterf/10)+1;
	}
	else
	{
		counterf = counterf/10;
	}
	radio_num = counterf/2;            // ���ռ�ձ�
	TIM2_PWMOutput_Init();             // ���²���PWM��
	fre0 =fre1;
	Filter1(0, 0.842);                     // (mode)���Ʒ�ʽmode, Ʒ������q
	Filter2(0, 0.842);                     // (mode)���Ʒ�ʽmode, Ʒ������q
	Delay_1ms(50);
	GPIOB->ODR =0;

	while(1)
	{
		// ��ʾ��ǰƵ��ֵ
		sprintf((char*)buf, "%6d", fre1);   // ������ת���ַ�����
		WriteString (2, 7, buf);            // ��1602������ʾ��ֹƵ�ʵ�ֵ

		Delay_1ms(100);                     // ��ʱ100ms
	}
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
