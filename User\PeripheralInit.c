//-----------------------------------------------------------------
// File: PeripheralInit.c
// Description: STM32 peripheral initialization module (keyboard removed)
// Author: Modified for STM32F103C8T6
// Date: 2014-04-24
// Modified: Keyboard initialization removed
// Version: V1.1
// Hardware: STM32F103C8T6 + LCD1602 + MAX262
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// Header Files
//-----------------------------------------------------------------
#include <stm32f10x.h>
#include "PeripheralInit.h"
#include "PWMOutput.h"
#include "CharLCD.h"
//-----------------------------------------------------------------
// Function Definitions
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// void PeripheralInit(void)
//-----------------------------------------------------------------
// Description: Peripheral initialization function
// Parameters: None
// Return: None
// Global Variables: None
// Notes: Keyboard initialization removed
//-----------------------------------------------------------------
void PeripheralInit(void)
{
	LCM_Init();
// 	MAX262_GPIO_Init();
	TIM2_PWMOutput_Init();
}

void MAX262_GPIO_Init(void)               // MAX262 GPIO initialization
{
	GPIO_InitTypeDef GPIO_InitStructure;

	// Enable GPIO clock
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB|RCC_APB2Periph_GPIOE, ENABLE);
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8  | GPIO_Pin_9          // D0, D1 
															| GPIO_Pin_10 | GPIO_Pin_11         // A0, A1
															| GPIO_Pin_12 | GPIO_Pin_13;        // A2, A3
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;                // Push-pull output
	GPIO_Init(GPIOB,&GPIO_InitStructure);

	GPIO_InitStructure.GPIO_Pin=GPIO_Pin_14  | GPIO_Pin_15 ;        // EN, WR 
// 	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;                // Push-pull output
	GPIO_Init(GPIOB,&GPIO_InitStructure);

}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
